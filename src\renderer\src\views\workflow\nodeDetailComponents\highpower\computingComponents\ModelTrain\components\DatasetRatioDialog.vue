<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <LucideIcon name="Sparkles" class="w-5 h-5 text-primary" />
          数据集划分配置
        </DialogTitle>
        <DialogDescription>
          设置训练集、测试集、验证集和支持集的数据划分比例，确保总和为100%
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4 py-4">
        <!-- 比例配置 -->
        <div class="space-y-3">
          <div class="grid grid-cols-2 gap-3">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <Label class="text-sm font-medium">训练集</Label>
              <Input
                v-model.number="ratios.train"
                type="number"
                min="0"
                max="100"
                class="w-15 h-7 text-center text-xs ml-auto"
                @input="handleRatioChange"
              />
              <span class="text-xs text-muted-foreground">%</span>
            </div>

            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-red-500 rounded-full"></div>
              <Label class="text-sm font-medium">测试集</Label>
              <Input
                v-model.number="ratios.test"
                type="number"
                min="0"
                max="100"
                class="w-15 h-7 text-center text-xs ml-auto"
                @input="handleRatioChange"
              />
              <span class="text-xs text-muted-foreground">%</span>
            </div>

            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <Label class="text-sm font-medium">验证集</Label>
              <Input
                v-model.number="ratios.val"
                type="number"
                min="0"
                max="100"
                class="w-15 h-7 text-center text-xs ml-auto"
                @input="handleRatioChange"
              />
              <span class="text-xs text-muted-foreground">%</span>
            </div>

            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
              <Label class="text-sm font-medium">支持集</Label>
              <Input
                v-model.number="ratios.support"
                type="number"
                min="0"
                max="100"
                class="w-15 h-7 text-center text-xs ml-auto"
                @input="handleRatioChange"
              />
              <span class="text-xs text-muted-foreground">%</span>
            </div>
          </div>
        </div>

        <!-- 总计显示 -->
        <div class="flex items-center justify-between pt-2 border-t">
          <Label class="text-sm font-medium">总计</Label>
          <div class="flex items-center gap-2">
            <span
              class="text-sm font-medium px-2 py-1 rounded"
              :class="
                totalRatio === 100
                  ? 'text-green-600 bg-green-50'
                  : 'text-destructive bg-destructive/10'
              "
            >
              {{ totalRatio }}%
            </span>
          </div>
        </div>

        <!-- 警告信息 -->
        <div v-if="totalRatio !== 100" class="flex items-center gap-2 p-2 bg-muted rounded-md">
          <LucideIcon name="AlertTriangle" class="w-4 h-4 text-muted-foreground" />
          <span class="text-xs text-muted-foreground">比例总和必须等于100%</span>
        </div>

        <!-- 数据集信息 -->
        <div class="bg-muted/50 p-3 rounded-md">
          <div class="flex items-center gap-2 mb-2">
            <LucideIcon name="Info" class="w-4 h-4 text-muted-foreground" />
            <span class="text-sm font-medium">数据集预览</span>
          </div>
          <div class="text-xs text-muted-foreground space-y-1">
            <div class="flex justify-between">
              <span>总数据量:</span>
              <span class="font-medium">{{ props.totalDataCount }} 个文件</span>
            </div>
            <div v-if="props.totalDataCount > 0" class="grid grid-cols-2 gap-2 mt-2">
              <div class="flex items-center gap-1">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>训练集: {{ Math.floor((props.totalDataCount * ratios.train) / 100) }}</span>
              </div>
              <div class="flex items-center gap-1">
                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>测试集: {{ Math.floor((props.totalDataCount * ratios.test) / 100) }}</span>
              </div>
              <div class="flex items-center gap-1">
                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>验证集: {{ Math.floor((props.totalDataCount * ratios.val) / 100) }}</span>
              </div>
              <div class="flex items-center gap-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>支持集: {{ Math.floor((props.totalDataCount * ratios.support) / 100) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="gap-2">
        <Button variant="outline" @click="handleCancel">取消</Button>
        <Button variant="outline" @click="handleReset">重置默认</Button>
        <Button :disabled="totalRatio !== 100" @click="handleConfirm">确定配置</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, watch, shallowRef } from 'vue'
import { LucideIcon } from '@renderer/components'
import { useDatasetRatio } from '../composables/useDatasetRatio'

// 定义比例配置接口
interface DatasetRatios {
  train: number
  test: number
  val: number
  support: number
}

// 定义props
const props = defineProps<{
  totalDataCount: number
  initialRatios?: DatasetRatios
}>()

// 使用 defineModel 来处理对话框开关状态
const isOpen = defineModel<boolean>('open', { default: false })

// 定义emit
const emit = defineEmits<{
  confirm: [ratios: DatasetRatios]
  cancel: []
}>()

// 优化：使用 computed 确保响应式更新
const totalDataCountRef = computed(() => props.totalDataCount)
const initialRatiosRef = computed(() => props.initialRatios)

// 使用组合式函数
const { ratios, totalRatio, handleRatioChange, handleReset, handleConfirm, handleCancel } =
  useDatasetRatio(totalDataCountRef, initialRatiosRef, isOpen, emit)
</script>

<style scoped></style>
