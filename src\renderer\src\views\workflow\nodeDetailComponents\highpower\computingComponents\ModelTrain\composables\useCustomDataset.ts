import { computed, ref, watch, type Ref, shallowRef, shallowReactive } from 'vue'
import { useDebounceFn } from '@vueuse/core'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

export function useCustomDataset(
  datasetData: Ref<DatasetData>,
  isOpen: Ref<boolean>,
  emit: ((event: 'confirm', datasetData: DatasetData) => void) &
    ((event: 'cancel') => void) &
    ((event: 'chart-click', item: DatasetItem) => void),
) {
  // 数据集类型配置
  const datasetTypes = [
    {
      key: 'train' as const,
      label: '训练集',
      colorClass: 'bg-green-500',
      activeClass: 'bg-green-50 text-green-700 border-green-300',
      dragOverClass: 'border-green-400 bg-green-50',
      itemBgClass: 'bg-green-50 border-green-200',
    },
    {
      key: 'test' as const,
      label: '测试集',
      colorClass: 'bg-red-500',
      activeClass: 'bg-red-50 text-red-700 border-red-300',
      dragOverClass: 'border-red-400 bg-red-50',
      itemBgClass: 'bg-red-50 border-red-200',
    },
    {
      key: 'val' as const,
      label: '验证集',
      colorClass: 'bg-yellow-500',
      activeClass: 'bg-yellow-50 text-yellow-700 border-yellow-300',
      dragOverClass: 'border-yellow-400 bg-yellow-50',
      itemBgClass: 'bg-yellow-50 border-yellow-200',
    },
    {
      key: 'support' as const,
      label: '支持集',
      colorClass: 'bg-gray-400',
      activeClass: 'bg-gray-50 text-gray-700 border-gray-300',
      dragOverClass: 'border-gray-400 bg-gray-50',
      itemBgClass: 'bg-gray-50 border-gray-200',
    },
  ]

  // 使用 shallowRef 和 shallowReactive 减少深度响应式开销
  const localDatasetItems = shallowRef<DatasetItem[]>([])
  const activeFilter = ref<'all' | 'train' | 'test' | 'val' | 'support'>('all')
  const searchQuery = ref('')
  const draggedItem = ref<DatasetItem | null>(null)
  const dragOverType = ref<string | null>(null)
  const chartDialogOpen = ref(false)
  const selectedDatasetItem = ref<DatasetItem | null>(null)

  // 使用缓存减少重复计算
  const typeCountCache = shallowReactive<Record<string, number>>({})

  // 监听 props 变化，更新本地数据
  const updateLocalDatasetItems = (newData: DatasetData) => {
    if (newData) {
      localDatasetItems.value = [
        ...newData.trainData,
        ...newData.testData,
        ...newData.valData,
        ...newData.supportData,
      ]
    }
  }

  watch(() => datasetData.value, updateLocalDatasetItems, { immediate: true })

  // 监听弹框打开状态，重置本地数据
  watch(isOpen, (isOpenValue) => {
    if (isOpenValue && datasetData.value) {
      updateLocalDatasetItems(datasetData.value)
      activeFilter.value = 'all'
      searchQuery.value = ''
    }
  })

  // 防抖搜索函数
  const debouncedSearch = ref('')
  const updateDebouncedSearch = useDebounceFn((query: string) => {
    debouncedSearch.value = query
  }, 300)

  // 监听搜索查询变化
  watch(searchQuery, (newQuery) => {
    updateDebouncedSearch(newQuery)
  })

  // 计算属性：筛选后的数据
  const filteredItems = computed(() => {
    let items = localDatasetItems.value

    // 搜索筛选
    if (debouncedSearch.value.trim()) {
      const query = debouncedSearch.value.toLowerCase()
      items = items.filter(
        (item) =>
          item.name.toLowerCase().includes(query) ||
          (item.rawData?.file_path || '').toLowerCase().includes(query),
      )
    }

    return items
  })

  // 获取指定类型的数据集数量
  const getTypeCount = (type: string): number => {
    // 简单的缓存机制，避免重复计算
    if (typeCountCache[type] === undefined) {
      typeCountCache[type] = localDatasetItems.value.filter((item) => item.type === type).length
    }
    return typeCountCache[type]
  }

  // 当数据变化时清空缓存
  watch(
    localDatasetItems,
    () => {
      Object.keys(typeCountCache).forEach((key) => {
        delete typeCountCache[key]
      })
    },
    { flush: 'sync' },
  )

  // 获取指定类型的筛选数据
  const getFilteredItems = (type: string) => {
    return filteredItems.value.filter((item) => item.type === type)
  }

  // 获取类型标签
  const getTypeLabel = (type: string): string => {
    const typeConfig = datasetTypes.find((t) => t.key === type)
    return typeConfig?.label || type
  }

  // 获取Badge样式类
  const getBadgeClass = (type: string): string => {
    const classes = {
      train: 'border-green-300 bg-green-50 text-green-700',
      test: 'border-red-300 bg-red-50 text-red-700',
      val: 'border-yellow-300 bg-yellow-50 text-yellow-700',
      support: 'border-gray-300 bg-gray-50 text-gray-700',
    }
    return classes[type as keyof typeof classes] || classes.support
  }

  // 获取数据项背景样式类
  const getItemBgClass = (type: string): string => {
    const typeConfig = datasetTypes.find((t) => t.key === type)
    return typeConfig?.itemBgClass || 'bg-white border-neutral-200'
  }

  // 获取短路径
  const getShortPath = (path?: string): string => {
    if (!path) return '未知路径'
    const parts = path.split('/')
    if (parts.length > 3) {
      return `.../${parts.slice(-2).join('/')}`
    }
    return path
  }

  // 设置活动筛选器
  const setActiveFilter = (filter: 'all' | 'train' | 'test' | 'val' | 'support') => {
    activeFilter.value = filter
  }

  // 获取当前活动类型的配置
  const getActiveTypeConfig = () => {
    if (activeFilter.value === 'all') return null
    return datasetTypes.find((type) => type.key === activeFilter.value)
  }

  // 拖拽处理
  const handleDragStart = (event: DragEvent, item: DatasetItem) => {
    draggedItem.value = item
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/plain', item.id)
    }
  }

  const handleDragEnd = () => {
    draggedItem.value = null
    dragOverType.value = null
  }

  const handleDragOver = (type: string) => {
    dragOverType.value = type
  }

  const handleDragLeave = () => {
    dragOverType.value = null
  }

  const handleDrop = (event: DragEvent, targetType: 'train' | 'test' | 'val' | 'support') => {
    event.preventDefault()
    dragOverType.value = null

    if (draggedItem.value && draggedItem.value.type !== targetType) {
      changeItemType(draggedItem.value.id, targetType)
    }

    draggedItem.value = null
  }

  // 改变数据集项的类型
  const changeItemType = (itemId: string, newType: 'train' | 'test' | 'val' | 'support') => {
    const item = localDatasetItems.value.find((item) => item.id === itemId)
    if (item) {
      item.type = newType
    }
  }

  // 处理图表点击
  const handleChartClick = (item: DatasetItem) => {
    selectedDatasetItem.value = item
    chartDialogOpen.value = true
    emit('chart-click', item)
  }

  // 处理确认
  const handleConfirm = () => {
    // 根据类型重新分组数据
    const newDatasetData: DatasetData = {
      trainData: localDatasetItems.value.filter((item) => item.type === 'train'),
      testData: localDatasetItems.value.filter((item) => item.type === 'test'),
      valData: localDatasetItems.value.filter((item) => item.type === 'val'),
      supportData: localDatasetItems.value.filter((item) => item.type === 'support'),
    }

    emit('confirm', newDatasetData)
    isOpen.value = false
  }

  // 处理取消
  const handleCancel = () => {
    emit('cancel')
    isOpen.value = false
  }

  return {
    // 配置
    datasetTypes,

    // 状态
    localDatasetItems,
    activeFilter,
    searchQuery,
    draggedItem,
    dragOverType,
    chartDialogOpen,
    selectedDatasetItem,

    // 计算属性
    filteredItems,

    // 方法
    getTypeCount,
    getFilteredItems,
    getTypeLabel,
    getBadgeClass,
    getItemBgClass,
    getShortPath,
    setActiveFilter,
    getActiveTypeConfig,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    changeItemType,
    handleChartClick,
    handleConfirm,
    handleCancel,
  }
}
