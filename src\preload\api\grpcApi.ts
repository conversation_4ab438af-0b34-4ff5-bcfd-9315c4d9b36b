import { ipc<PERSON><PERSON><PERSON> } from 'electron'

/**
 * gRPC相关API
 */
export const grpcApi = {
  /**
   * 更新认证信息
   */
  updateAuthInfo: (userId: string, token: string) => {
    return ipcRenderer.invoke('update-auth-info', { userId, token })
  },
  /**
   * 调用gRPC API
   */
  call: (apiName: string, params: any) => {
    return ipcRenderer.invoke('grpc-call', {
      apiName,
      params,
    })
  },

  /**
   * 提交gRPC同步任务
   */
  default: (serviceName: string, serverId: string, isSave: boolean, params: any) => {
    return ipcRenderer.invoke('grpc-default', {
      serviceName,
      serverId,
      isSave,
      params,
    })
  },

  /**
   * 提交gRPC计算任务
   */
  submit: (serviceName: string, serverId: string, isSave: boolean, params: any) => {
    return ipcRenderer.invoke('grpc-submit', {
      serviceName,
      serverId,
      isSave,
      params,
    })
  },

  /**
   * 调用gRPC Agent
   */
  agent: (
    serviceName: string,
    serverId: string,
    sessionId: string,
    modelType: string,
    isStreamResponse: boolean,
    messages: any[],
    callBack: (result: any, type: string | undefined) => void,
  ) => {
    try {
      const handler = (_event: any, { type, data, error }: any) => {
        if (type === 'data') {
          callBack('data', data)
        } else if (type === 'end') {
          callBack('end', undefined)
          ipcRenderer.removeListener(sessionId, handler)
        } else if (type === 'error') {
          callBack(error, 'error')
          ipcRenderer.removeListener(sessionId, handler)
        }
      }

      ipcRenderer.on(sessionId, handler)
      ipcRenderer.send('grpc-agent', {
        serviceName,
        serverId,
        sessionId,
        modelType,
        isStreamResponse,
        messages,
      })
    } catch (error) {
      console.error(error)
    }
  },

  // grpc setClient - 流式长连接
  setClient: (
    userName: string,
    loginIp: string,
    password: string,
    expiredTime: number,
    callBack: (result: any, type: string) => void,
  ) => {
    try {
      // 移除之前的监听器
      ipcRenderer.removeAllListeners('client-channel-event')

      // 使用固定的通道名称
      const handler = (_event: any, { type, data, error }: any) => {
        if (type === 'data' && data) {
          callBack(data, 'data')
        } else if (type === 'end') {
          callBack(undefined, 'end')
          // 连接结束时移除监听器
          ipcRenderer.removeListener('client-channel-event', handler)
        } else if (type === 'error') {
          callBack(error || '未知错误', 'error')
        }
      }

      // 添加监听器
      ipcRenderer.on('client-channel-event', handler)

      // 发送请求
      ipcRenderer.send('grpc-setClient', {
        userName,
        loginIp,
        password,
        expiredTime,
      })

      // 返回控制对象
      return {
        disconnect: () => {
          ipcRenderer.send('grpc-disconnect-client')
          ipcRenderer.removeListener('client-channel-event', handler)
        },
      }
    } catch (error) {
      console.error(error)
      callBack(error instanceof Error ? error.message : '未知错误', 'error')
      return {
        disconnect: () => {},
      }
    }
  },

  // 断开连接
  disconnectClient: (sessionId: string) => {
    return new Promise((resolve) => {
      const responseHandler = (_event: any, response: any) => {
        ipcRenderer.removeListener('grpc-disconnect-response', responseHandler)
        resolve(response)
      }

      ipcRenderer.on('grpc-disconnect-response', responseHandler)
      ipcRenderer.send('grpc-disconnect-client', { sessionId })
    })
  },

  /**
   * 获取gRPC连接状态
   */
  getStatus: () => ipcRenderer.invoke('get-grpc-status'),

  /**
   * 更新Linker API
   */
  updateLinkerApi: (newLinkerApi: string) => ipcRenderer.invoke('update-linker-api', newLinkerApi),

  /**
   * 重启应用
   */
  restartApp: () => ipcRenderer.invoke('restart-app'),

  // 停止生成
  stopGeneration: (sessionId: string) => {
    ipcRenderer.send('grpc-stop-generation', { sessionId })
  },
  // 开启文件上传 param: 参数
  fileUpload: (param: any, callBack) => {
    ipcRenderer.send('grpc-client-stream', param)
    ipcRenderer.on('client-upload-status', (event, data) => {
      callBack(data)
    })
  },

  uploadCsvFile: (params: any, callBack) => {
    ipcRenderer.send('upload-csv-file', params) // 向主进程发送上传请求

    // 监听主进程的多次回复
    ipcRenderer.on('upload-status', (event, data) => {
      if (data.uploadId === params.uploadId) {
        callBack(data)
      }
    })
  },

  /**
   * 下载文件 - 服务端流式传输
   * @param storedFilepath 存储的文件路径
   * @param chunkSize 分块大小，默认1MB
   * @param callBack 回调函数，处理流式响应
   * @returns 控制对象，包含取消下载的方法
   */
  downloadFile: (
    storedFilepath: string,
    chunkSize: number = 1024 * 1024,
    callBack: (result: any, type: string) => void,
  ) => {
    try {
      // 生成唯一的下载ID
      const downloadId = `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // 移除之前的监听器
      ipcRenderer.removeAllListeners('download-file-chunk')

      // 添加监听器处理下载响应
      const handler = (
        _event: any,
        { downloadId: responseDownloadId, type, chunk, error }: any,
      ) => {
        if (responseDownloadId === downloadId) {
          if (type === 'data' && chunk) {
            callBack(chunk, 'data')
          } else if (type === 'end') {
            callBack(undefined, 'end')
            // 下载结束时移除监听器
            ipcRenderer.removeListener('download-file-chunk', handler)
          } else if (type === 'error') {
            callBack(error || '下载失败', 'error')
            ipcRenderer.removeListener('download-file-chunk', handler)
          }
        }
      }

      // 添加监听器
      ipcRenderer.on('download-file-chunk', handler)

      // 发送下载请求
      ipcRenderer.send('grpc-download-file', {
        storedFilepath,
        chunkSize,
        downloadId,
      })

      // 返回控制对象
      return {
        downloadId,
        cancel: () => {
          ipcRenderer.removeListener('download-file-chunk', handler)
        },
      }
    } catch (error) {
      console.error('downloadFile error:', error)
      callBack(error instanceof Error ? error.message : '下载失败', 'error')
      return {
        downloadId: '',
        cancel: () => {},
      }
    }
  },
}
