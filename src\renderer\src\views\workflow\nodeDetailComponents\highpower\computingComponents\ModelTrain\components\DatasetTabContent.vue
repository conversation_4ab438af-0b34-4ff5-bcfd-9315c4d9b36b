<template>
  <div class="space-y-4">
    <!-- 全选控制 - 作为收缩/展开触发器 -->
    <CollapsibleTrigger class="w-full">
      <div
        class="flex items-center justify-between p-2 bg-neutral-50 rounded border hover:bg-neutral-100 transition-colors cursor-pointer"
      >
        <!-- 左侧：颜色圆点、名称和已选择项数 -->
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full" :class="getColorDotClass(tabKey)"></div>
            <div class="text-xs text-muted-foreground">
              {{ tabLabel }}
            </div>
          </div>
          <Badge variant="secondary" class="bg-neutral-100 text-neutral-600 text-xs">
            已选择 {{ selectedCount }} / {{ totalCount }} 项
          </Badge>
        </div>

        <!-- 右侧：全选和展开图标 -->
        <div class="flex items-center space-x-2">
          <Checkbox
            :model-value="isAllSelected"
            :indeterminate="isIndeterminate"
            :disabled="isProcessing || items.length === 0"
            @update:model-value="handleSelectAll"
            @click.stop
          />
          <Label class="text-xs font-medium">全选</Label>
          <LucideIcon
            name="ChevronDown"
            class="w-4 h-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
          />
        </div>
      </div>
    </CollapsibleTrigger>

    <!-- 数据项列表 - 包装在 CollapsibleContent 中 -->
    <CollapsibleContent>
      <div v-if="items.length === 0" class="text-center py-2 text-muted-foreground">
        <div class="flex flex-col items-center space-y-1">
          <LucideIcon name="Database" class="w-8 h-8 text-muted-foreground/50" />
          <p class="text-xs">暂无{{ tabLabel }}数据</p>
        </div>
      </div>

      <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mt-2">
        <DatasetItem
          v-for="(item, index) in items"
          :key="item.id"
          :item="item"
          :is-processing="isProcessing"
          @update:selected="handleItemSelection"
          @chart-click="handleChartClick"
        />
      </div>
    </CollapsibleContent>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { LucideIcon } from '@renderer/components'
import DatasetItem from './DatasetItem.vue'

// 定义数据集项的类型
interface DatasetItemType {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义props
const props = defineProps<{
  tabKey: string
  tabLabel: string
  items: DatasetItemType[]
  isProcessing?: boolean
}>()

// 定义emit
const emit = defineEmits<{
  'update:selected-items': [tabKey: string, selectedItems: string[]]
  'select-all': [tabKey: string, selectAll: boolean]
  'chart-click': [item: DatasetItemType]
}>()

// 计算属性
const totalCount = computed(() => props.items.length)
const selectedCount = computed(() => props.items.filter((item) => item.selected).length)
const isAllSelected = computed(
  () => totalCount.value > 0 && selectedCount.value === totalCount.value,
)
const isIndeterminate = computed(
  () => selectedCount.value > 0 && selectedCount.value < totalCount.value,
)

// 获取颜色圆点样式类
const getColorDotClass = (tabKey: string): string => {
  const colorClasses = {
    allData: 'bg-blue-400', // 全部 - 灰色
    trainData: 'bg-green-500', // 训练集 - 绿色
    testData: 'bg-red-500', // 测试集 - 红色
    valData: 'bg-yellow-500', // 验证集 - 黄色
    supportData: 'bg-gray-400', // 支持集 - 灰色
  }
  return colorClasses[tabKey as keyof typeof colorClasses] || colorClasses.allData
}

// 处理全选/取消全选
const handleSelectAll = (selectAll: boolean) => {
  emit('select-all', props.tabKey, selectAll)
}

// 处理单个项目选择
const handleItemSelection = (itemId: string, selected: boolean) => {
  const currentSelectedItems = props.items
    .filter((item) => item.selected && item.id !== itemId)
    .map((item) => item.id)

  if (selected) {
    currentSelectedItems.push(itemId)
  }

  emit('update:selected-items', props.tabKey, currentSelectedItems)
}

// 处理图表点击
const handleChartClick = (item: DatasetItemType) => {
  emit('chart-click', item)
}
</script>

<style scoped></style>
