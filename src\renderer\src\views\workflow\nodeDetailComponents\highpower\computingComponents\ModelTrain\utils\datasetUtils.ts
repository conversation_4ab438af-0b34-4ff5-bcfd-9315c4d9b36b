// 数据集工具函数
export interface DatasetRatios {
  train: number
  test: number
  val: number
  support: number
}

export interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

export interface DatasetData {
  allData: DatasetItem[] // 动态合并的所有数据，用于显示
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

/**
 * 检查是否有任何数据集数据
 */
export const hasAnyDatasetData = (datasetData: DatasetData): boolean => {
  return (
    datasetData.trainData.length > 0 ||
    datasetData.testData.length > 0 ||
    datasetData.valData.length > 0 ||
    datasetData.supportData.length > 0
  )
}

/**
 * 检查是否有原始数据
 */
export const hasRawData = (result: any): boolean => {
  return result && Array.isArray(result) && result.length > 0
}

/**
 * 检查是否有任何可用数据（数据集数据或原始数据）
 */
export const hasAnyAvailableData = (datasetData: DatasetData, rawResult: any): boolean => {
  return hasAnyDatasetData(datasetData) || hasRawData(rawResult)
}

/**
 * 创建数据集项
 */
export const createDatasetItem = (
  item: any,
  index: number,
  type: DatasetItem['type'] = 'train',
): DatasetItem => {
  const fileName = item.file_path && item.file_path.split('/').pop()

  // 计算循环范围
  let startCycle = 1
  let endCycle = 100

  if (item.cycle_capacity_array && Array.isArray(item.cycle_capacity_array)) {
    const cycleArray = item.cycle_capacity_array
    startCycle = cycleArray[0]?.[0] || 1
    endCycle = cycleArray[cycleArray.length - 1]?.[0] || 100
  }

  return {
    id: `dataset_${index}`,
    name: fileName || `数据集_${index + 1}`,
    type,
    selected: true,
    isRecommended: false,
    startCycle,
    endCycle,
    rawData: {
      file_path: item.file_path,
      cycle_capacity_array: item.cycle_capacity_array,
      error: item.error,
    },
  }
}

/**
 * 根据比例分配数据集
 */
export const distributeDatasetByRatios = (
  items: DatasetItem[],
  ratios: DatasetRatios,
): DatasetData => {
  const totalCount = items.length
  const trainCount = Math.floor((totalCount * ratios.train) / 100)
  const testCount = Math.floor((totalCount * ratios.test) / 100)
  const valCount = Math.floor((totalCount * ratios.val) / 100)
  const supportCount = totalCount - trainCount - testCount - valCount

  let currentIndex = 0

  const trainData = items.slice(currentIndex, currentIndex + trainCount).map((item) => ({
    ...item,
    type: 'train' as const,
    isRecommended: false,
  }))
  currentIndex += trainCount

  const testData = items.slice(currentIndex, currentIndex + testCount).map((item) => ({
    ...item,
    type: 'test' as const,
    isRecommended: false,
  }))
  currentIndex += testCount

  const valData = items.slice(currentIndex, currentIndex + valCount).map((item) => ({
    ...item,
    type: 'val' as const,
    isRecommended: false,
  }))
  currentIndex += valCount

  const supportData = items.slice(currentIndex).map((item) => ({
    ...item,
    type: 'support' as const,
    isRecommended: false,
  }))

  return {
    allData: [], // 动态计算，不在这里设置
    trainData,
    testData,
    valData,
    supportData,
  }
}

/**
 * 将API结果转换为数据集格式
 */
export const convertApiDataToDataset = (
  apiResult: any[],
  ratios: DatasetRatios = { train: 60, test: 20, val: 10, support: 10 },
): DatasetData => {
  if (!apiResult || !Array.isArray(apiResult)) {
    return {
      allData: [],
      trainData: [],
      testData: [],
      valData: [],
      supportData: [],
    }
  }

  // 创建数据集项
  const datasetItems = apiResult.map((item, index) => createDatasetItem(item, index))

  // 根据比例分配
  return distributeDatasetByRatios(datasetItems, ratios)
}

/**
 * 生成训练所需的数据格式
 */
export const generateTrainingData = (datasetData: DatasetData) => {
  const formatDatasetItems = (items: DatasetItem[]) => {
    return items
      .filter((item) => item.selected && item.rawData?.file_path)
      .map((item) => ({
        file_path: item.rawData!.file_path,
        start_cycle: item.startCycle || 1,
        end_cycle: item.endCycle || 100,
      }))
  }

  return {
    All_data: [], // 通常为空，根据需要可以合并所有数据
    train_data: formatDatasetItems(datasetData.trainData),
    test_data: formatDatasetItems(datasetData.testData),
    val_data: formatDatasetItems(datasetData.valData),
    support_data: formatDatasetItems(datasetData.supportData),
  }
}
