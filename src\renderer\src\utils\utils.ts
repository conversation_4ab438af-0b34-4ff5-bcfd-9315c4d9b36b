import type { Updater } from '@tanstack/vue-table'
import type { Ref } from 'vue'
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import dayjs from 'dayjs'

import type {
  SaveFileOptions,
  OpenFileOptions,
  EncryptFileOptions,
  FileOperationResult,
} from '@renderer/config/interface/file'
import { FileService } from '@renderer/config/api/grpc/fileService'
import { toast } from 'vue-sonner'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function valueUpdater<T extends Updater<any>>(updaterOrValue: T, ref: Ref) {
  ref.value = typeof updaterOrValue === 'function' ? updaterOrValue(ref.value) : updaterOrValue
}

/**
 * 将字符串从 snake_case 转换为 camelCase
 * @param str snake_case 格式的字符串
 * @returns camelCase 格式的字符串
 */
export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 递归地将对象的所有属性从 snake_case 转换为 camelCase
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export function convertToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertToCamelCase(item))
  }
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const camelKey = toCamelCase(key)
      result[camelKey] = convertToCamelCase(obj[key])
      return result
    }, {} as any)
  }
  return obj
}

/**
 * 检查 electronAPI 是否可用
 * @returns 检查结果
 */
const checkElectronAPI = (): boolean => {
  if (!window.electronAPI) {
    console.error('electronAPI 不可用，请确保在 Electron 环境中运行')
    return false
  }
  return true
}

/**
 * 保存文件
 * @param options 保存文件选项
 * @returns 保存结果
 */
export const saveFile = async (options: SaveFileOptions): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.saveFile(options)
  } catch (error) {
    console.error('保存文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 打开文件对话框
 * @param options 打开文件选项
 * @returns 打开结果
 */
export const openFile = async (options: OpenFileOptions = {}): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.openFile(options)
  } catch (error) {
    console.error('打开文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 读取文件内容
 * @param filePath 文件路径或读取选项
 * @returns 文件内容
 */
export const readFile = async (
  filePath: string | { path: string; encoding?: string; isBinary?: boolean },
): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.readFile(filePath)
  } catch (error) {
    console.error('读取文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 加密并保存文件
 * @param options 加密文件选项
 * @returns 保存结果
 */
export const encryptAndSaveFile = async (
  options: EncryptFileOptions,
): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.encryptAndSaveFile(options)
  } catch (error) {
    console.error('加密保存文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}
/**
 * 通用文件下载方法
 * @param filePath 服务器上的文件路径
 * @param options 下载选项
 * @returns Promise<boolean> 下载是否成功
 */
export const downloadFileFromServer = async (
  filePath: string,
  options: {
    defaultFileName?: string
    fileExtension?: string
    fileTypeDescription?: string
    showProgress?: boolean
    onProgress?: (progress: number, fileName?: string) => void
  } = {},
): Promise<boolean> => {
  if (!checkElectronAPI()) {
    toast.error('下载失败', {
      description: 'electronAPI 不可用',
    })
    return false
  }

  try {
    console.log('=== downloadFileFromServer 开始 ===')
    console.log('文件路径:', filePath)
    console.log('选项:', options)

    // 设置默认选项
    const {
      defaultFileName = `downloaded_file_${Date.now()}`,
      fileExtension = '*',
      fileTypeDescription = 'All Files',
      showProgress = true,
      onProgress,
    } = options

    console.log('解析后的选项:', {
      defaultFileName,
      fileExtension,
      fileTypeDescription,
      showProgress,
    })

    // 弹出文件保存对话框
    const saveOptions = {
      title: '保存下载的文件',
      defaultPath: defaultFileName,
      filters: [
        { name: fileTypeDescription, extensions: [fileExtension] },
        { name: 'All Files', extensions: ['*'] },
      ],
    }

    console.log('保存对话框选项:', saveOptions)
    console.log('调用 window.electronAPI.openSaveFileDialog...')

    const savePath = await window.electronAPI.openSaveFileDialog(saveOptions)
    console.log('保存对话框返回结果:', savePath)

    if (!savePath || savePath.canceled) {
      console.log('用户取消了文件保存')
      return false
    }

    if (showProgress) {
      toast.info('开始下载文件...', {
        description: '正在从服务器下载文件',
      })
    }

    // 创建 FileService 实例
    const fileService = new FileService()

    // 使用 fileService 下载文件
    const result = await fileService.downloadFile(filePath, (progress, fileName) => {
      console.log(`下载进度: ${progress.toFixed(1)}% - ${fileName}`)
      if (onProgress) {
        onProgress(progress, fileName)
      }
    })

    console.log('文件下载完成:', {
      fileName: result.fileName,
      fileSize: result.content.length,
      sha256: result.sha256,
    })

    // 将下载的文件保存到用户选择的路径
    const saveResult = await window.electronAPI.saveFile({
      filePath: savePath.filePath,
      content: result.content,
      encoding: null, // 二进制文件
    })

    if (saveResult.success) {
      if (showProgress) {
        toast.success('文件下载完成', {
          description: `文件已保存到: ${savePath.filePath}`,
        })
      }
      return true
    } else {
      throw new Error(saveResult.error || '保存文件失败')
    }
  } catch (error: any) {
    console.error('下载文件失败:', error)
    toast.error('下载失败', {
      description: error.message || '下载文件时发生错误',
    })
    return false
  }
}

/**
 * 下载模型文件的便捷方法
 * @param filePath 服务器上的模型文件路径
 * @param onProgress 进度回调函数
 * @returns Promise<boolean> 下载是否成功
 */
export const downloadModelFile = async (
  filePath: string,
  onProgress?: (progress: number, fileName?: string) => void,
): Promise<boolean> => {
  return downloadFileFromServer(filePath, {
    defaultFileName: `exported_model_${Date.now()}.ml`,
    fileExtension: 'ml',
    fileTypeDescription: 'Model Files',
    showProgress: true,
    onProgress,
  })
}

export const formatTimestamp = (timestamp?: string | number) => {
  if (!timestamp) return ''
  return dayjs(Number(timestamp) * (String(timestamp).length === 10 ? 1000 : 1)).format(
    'YYYY-MM-DD HH:mm:ss',
  )
}

// 格式化日期
export const formatDate = (date: Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 计算持续时间
export const formatDuration = (startTime: number, endTime: number) => {
  if (!startTime || startTime === 0) return '未开始'
  const start = startTime * 1000
  const end = endTime && endTime !== 0 ? endTime * 1000 : Date.now()
  const durationMs = end - start
  if (durationMs < 0) return '0h 0m 0s'
  const hours = Math.floor(durationMs / (1000 * 60 * 60))
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((durationMs % (1000 * 60)) / 1000)
  return `${hours}h ${minutes}m ${seconds}s`
}
