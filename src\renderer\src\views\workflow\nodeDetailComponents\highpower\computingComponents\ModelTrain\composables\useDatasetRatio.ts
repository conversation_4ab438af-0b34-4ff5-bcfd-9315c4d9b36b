import { computed, reactive, watch, type Ref } from 'vue'
import { useDebounceFn } from '@vueuse/core'

// 定义比例配置接口
interface DatasetRatios {
  train: number
  test: number
  val: number
  support: number
}

export function useDatasetRatio(
  totalDataCount: Ref<number>,
  initialRatios: Ref<DatasetRatios | undefined>,
  isOpen: Ref<boolean>,
  emit: ((event: 'confirm', ratios: DatasetRatios) => void) & ((event: 'cancel') => void),
) {
  // 默认比例配置
  const defaultRatios: DatasetRatios = {
    train: 60,
    test: 20,
    val: 10,
    support: 10,
  }

  // 当前比例配置
  const ratios = reactive<DatasetRatios>({
    ...defaultRatios,
    ...(initialRatios.value || {}),
  })

  // 计算总比例
  const totalRatio = computed(() => {
    return ratios.train + ratios.test + ratios.val + ratios.support
  })

  // 防抖处理比例变化，减少频繁计算
  const handleRatioChange = useDebounceFn(() => {
    // 确保所有值都是数字
    Object.keys(ratios).forEach((key) => {
      const value = ratios[key as keyof DatasetRatios]
      if (isNaN(value) || value < 0) {
        ratios[key as keyof DatasetRatios] = 0
      } else if (value > 100) {
        ratios[key as keyof DatasetRatios] = 100
      }
    })
  }, 200)

  // 重置为默认值
  const handleReset = () => {
    Object.assign(ratios, defaultRatios)
  }

  // 确认配置
  const handleConfirm = () => {
    if (totalRatio.value === 100) {
      emit('confirm', { ...ratios })
      isOpen.value = false
    }
  }

  // 取消配置
  const handleCancel = () => {
    emit('cancel')
    isOpen.value = false
    // 恢复为初始值或默认值
    Object.assign(ratios, { ...defaultRatios, ...(initialRatios.value || {}) })
  }

  // 监听对话框打开状态，恢复为初始值
  watch(isOpen, (newValue) => {
    if (newValue) {
      // 使用传入的初始值或默认值
      const currentInitialRatios = initialRatios.value
      Object.assign(ratios, { ...defaultRatios, ...(currentInitialRatios || {}) })
    }
  })

  // 监听 initialRatios 变化，更新当前比例
  watch(
    () => initialRatios.value,
    (newRatios) => {
      if (newRatios && isOpen.value) {
        Object.assign(ratios, { ...defaultRatios, ...newRatios })
      }
    },
    { immediate: true },
  )

  return {
    // 状态
    ratios,
    defaultRatios,

    // 计算属性
    totalRatio,

    // 方法
    handleRatioChange,
    handleReset,
    handleConfirm,
    handleCancel,
  }
}
