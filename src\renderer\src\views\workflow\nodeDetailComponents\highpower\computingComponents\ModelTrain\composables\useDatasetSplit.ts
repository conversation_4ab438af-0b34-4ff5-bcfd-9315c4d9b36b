import { computed, ref, type Ref, shallowRef, watch } from 'vue'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  allData: DatasetItem[]
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

export function useDatasetSplit(datasetData: Ref<DatasetData>) {
  // 图表弹框相关状态
  const showChartDialog = ref(false)
  const selectedDataset = ref<DatasetItem | undefined>(undefined)

  // 当前选中的tabs（支持多选）
  const selectedTabs = ref(['allData'])

  // tabs配置
  const tabs = [
    { key: 'allData', label: '全部' },
    { key: 'trainData', label: '训练集' },
    { key: 'testData', label: '测试集' },
    { key: 'valData', label: '验证集' },
    { key: 'supportData', label: '支持集' },
  ]

  // 计算属性：检查是否有任何数据
  const hasAnyData = computed(() => {
    return (
      datasetData.value.trainData.length > 0 ||
      datasetData.value.testData.length > 0 ||
      datasetData.value.valData.length > 0 ||
      datasetData.value.supportData.length > 0
    )
  })

  // 使用 shallowRef 缓存合并后的数据，减少深度响应式开销
  const allDataCache = shallowRef<DatasetItem[]>([])

  // 手动更新缓存，避免频繁重新计算
  const updateAllDataCache = () => {
    allDataCache.value = [
      ...datasetData.value.trainData,
      ...datasetData.value.testData,
      ...datasetData.value.valData,
      ...datasetData.value.supportData,
    ]
  }

  // 监听数据变化，更新缓存
  watch(
    () => [
      datasetData.value.trainData.length,
      datasetData.value.testData.length,
      datasetData.value.valData.length,
      datasetData.value.supportData.length,
    ],
    updateAllDataCache,
    { immediate: true },
  )

  // 获取指定tab的数据项
  const getTabItems = (tabKey: string): DatasetItem[] => {
    if (tabKey === 'allData') {
      return allDataCache.value
    }
    return datasetData.value[tabKey as keyof DatasetData] || []
  }

  // 获取指定tab的总数量
  const getTotalCount = (tabKey: string): number => {
    return getTabItems(tabKey).length
  }

  // 获取指定tab的选中数量
  const getSelectedCount = (tabKey: string): number => {
    return getTabItems(tabKey).filter((item) => item.selected).length
  }

  // 获取指定tab的标签
  const getTabLabel = (tabKey: string): string => {
    const tab = tabs.find((t) => t.key === tabKey)
    return tab?.label || ''
  }

  // 获取选中tabs的显示标签
  const getSelectedTabsLabel = (): string => {
    if (selectedTabs.value.length === 0) {
      return '选择数据集'
    } else if (selectedTabs.value.length === 1) {
      return getTabLabel(selectedTabs.value[0])
    } else {
      return `已选择 ${selectedTabs.value.length} 个数据集`
    }
  }

  // 处理tab选择（多选）
  const handleTabSelection = (tabKey: string, checked: boolean) => {
    if (checked) {
      if (!selectedTabs.value.includes(tabKey)) {
        selectedTabs.value.push(tabKey)
      }
    } else {
      const index = selectedTabs.value.indexOf(tabKey)
      if (index > -1) {
        selectedTabs.value.splice(index, 1)
      }
    }
  }

  // 检查是否全选
  const isAllSelected = (tabKey: string): boolean => {
    const items = getTabItems(tabKey)
    return items.length > 0 && items.every((item) => item.selected)
  }

  // 检查是否半选状态
  const isIndeterminate = (tabKey: string): boolean => {
    const items = getTabItems(tabKey)
    const selectedCount = items.filter((item) => item.selected).length
    return selectedCount > 0 && selectedCount < items.length
  }

  // 处理选中项更新
  const handleUpdateSelectedItems = (tabKey: string, selectedItems: string[]) => {
    if (tabKey === 'allData') {
      // 如果是全部标签页，需要更新所有相关数据集
      const allItems = getTabItems(tabKey)
      allItems.forEach((item) => {
        item.selected = selectedItems.includes(item.id)
      })
    } else {
      // 其他标签页只更新自己的数据
      const items = getTabItems(tabKey)
      items.forEach((item) => {
        item.selected = selectedItems.includes(item.id)
      })
    }
  }

  // 处理全选/取消全选
  const handleSelectAll = (tabKey: string, selectAll: boolean) => {
    if (tabKey === 'allData') {
      // 如果是全部标签页，需要更新所有相关数据集
      datasetData.value.trainData.forEach((item) => {
        item.selected = selectAll
      })
      datasetData.value.testData.forEach((item) => {
        item.selected = selectAll
      })
      datasetData.value.valData.forEach((item) => {
        item.selected = selectAll
      })
      datasetData.value.supportData.forEach((item) => {
        item.selected = selectAll
      })
    } else {
      // 其他标签页只更新自己的数据
      const items = getTabItems(tabKey)
      items.forEach((item) => {
        item.selected = selectAll
      })
    }
  }

  // 处理图表点击
  const handleChartClick = (item: DatasetItem) => {
    console.log('点击图表按钮:', item)
    selectedDataset.value = item
    showChartDialog.value = true
  }

  return {
    // 状态
    showChartDialog,
    selectedDataset,
    selectedTabs,
    tabs,

    // 计算属性
    hasAnyData,

    // 方法
    getTabItems,
    getTotalCount,
    getSelectedCount,
    getTabLabel,
    getSelectedTabsLabel,
    handleTabSelection,
    isAllSelected,
    isIndeterminate,
    handleUpdateSelectedItems,
    handleSelectAll,
    handleChartClick,
  }
}
