<template>
  <Card v-if="shouldShowResult" class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="!!hasResult">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">
              训练结果
              <!-- <Badge v-if="hasResult" variant="secondary" class="ml-2">已完成</Badge> -->
            </CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">{{ hasResult ? '收起' : '查看' }}</span>
              <ChevronDown
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div class="mt-4 rounded-lg p-4 bg-white dark:bg-gray-900 shadow-sm">
            <div class="flex justify-between items-center mb-4">
              <div class="flex items-center gap-2">
                <h3 class="text-lg font-medium">
                  模型训练结果
                  <Badge :class="getStatusClass">{{ getStatusText }}</Badge>
                </h3>
              </div>

              <HoverCard>
                <HoverCardTrigger as-child>
                  <Button variant="outline" size="sm" class="flex items-center gap-1">
                    <InfoIcon class="h-4 w-4" />
                    <span>训练信息</span>
                  </Button>
                </HoverCardTrigger>
                <HoverCardContent class="w-80">
                  <div class="flex justify-between space-x-4">
                    <div class="space-y-1 flex-1">
                      <h4 class="text-sm font-semibold">模型训练结果</h4>
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">训练轮数:</span>
                        <span class="font-medium">{{ currentResultData?.epoch }} 轮</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">平均绝对误差:</span>
                        <span class="font-medium">
                          {{ formatValue(currentResultData?.train_mae) }}
                        </span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">训练损失:</span>
                        <span class="font-medium">
                          {{ formatValue(currentResultData?.train_loss) }}
                        </span>
                      </div>
                      <!-- <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">R²得分:</span>
                        <span class="font-medium">
                          {{ formatValue(currentResultData?.train_r2) }}
                        </span>
                      </div> -->
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">训练轮数</span>
                  <span class="font-semibold">{{ currentResultData?.epoch }} 轮</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">训练损失</span>
                  <span class="font-semibold">
                    {{ formatValue(currentResultData?.train_loss) }}
                  </span>
                </div>
              </div>

              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                    平均绝对误差
                  </span>
                  <span class="font-semibold">
                    {{ formatValue(currentResultData?.train_mae) }}
                  </span>
                </div>
                <!-- <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">R²得分</span>
                  <span class="font-semibold">{{ formatValue(currentResultData?.train_r2) }}</span>
                </div> -->
              </div>
            </div>

            <div class="mt-4 flex justify-end gap-2">
              <Button size="sm" variant="outline" @click="showTaskLogs">
                <FileText class="h-4 w-4 mr-2" />
                详细日志
              </Button>
              <Button size="sm" @click="showResultDetail">
                <ListIcon class="h-4 w-4 mr-2" />
                结果详情
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>

  <!-- 结果详情对话框 -->
  <Dialog v-model:open="isDialogOpen">
    <DialogContent class="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>模型训练结果详情</DialogTitle>
        <DialogDescription>训练完成的详细指标信息</DialogDescription>
      </DialogHeader>
      <div v-if="currentResultData" class="py-4 space-y-4">
        <div class="flex justify-between items-center mb-4">
          <div class="text-sm text-gray-500">训练指标详情</div>
          <Button size="sm" variant="outline" @click="copyResultToClipboard">
            <ClipboardCopyIcon class="h-4 w-4 mr-2" />
            复制结果
          </Button>
        </div>

        <div class="space-y-3">
          <div class="p-3 rounded-md bg-gray-50 dark:bg-gray-800">
            <div class="flex justify-between items-center">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">训练轮数 (Epoch)</span>
                <div class="text-xs text-gray-500 mt-1">完成的训练轮数</div>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-gray-900 dark:text-gray-100 font-mono text-lg">
                  {{ currentResultData.epoch || 0 }}
                </span>
                <Button
                  size="icon"
                  variant="ghost"
                  class="h-6 w-6"
                  @click="copyValueToClipboard(currentResultData.epoch)"
                >
                  <ClipboardCopyIcon class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          <div class="p-3 rounded-md bg-gray-50 dark:bg-gray-800">
            <div class="flex justify-between items-center">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">
                  训练损失 (Train Loss)
                </span>
                <div class="text-xs text-gray-500 mt-1">模型在训练集上的损失值</div>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-gray-900 dark:text-gray-100 font-mono">
                  {{ formatValue(currentResultData.train_loss) }}
                </span>
                <Button
                  size="icon"
                  variant="ghost"
                  class="h-6 w-6"
                  @click="copyValueToClipboard(currentResultData.train_loss)"
                >
                  <ClipboardCopyIcon class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          <div class="p-3 rounded-md bg-gray-50 dark:bg-gray-800">
            <div class="flex justify-between items-center">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">平均绝对误差 (MAE)</span>
                <div class="text-xs text-gray-500 mt-1">预测值与真实值的平均绝对差值</div>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-gray-900 dark:text-gray-100 font-mono">
                  {{ formatValue(currentResultData.train_mae) }}
                </span>
                <Button
                  size="icon"
                  variant="ghost"
                  class="h-6 w-6"
                  @click="copyValueToClipboard(currentResultData.train_mae)"
                >
                  <ClipboardCopyIcon class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          <div class="p-3 rounded-md bg-gray-50 dark:bg-gray-800">
            <div class="flex justify-between items-center">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">
                  R²决定系数 (R² Score)
                </span>
                <div class="text-xs text-gray-500 mt-1">模型解释数据变异性的比例</div>
              </div>
              <div class="flex items-center gap-2">
                <span
                  class="text-gray-900 dark:text-gray-100 font-mono"
                  :class="getR2ScoreClass(currentResultData.train_r2)"
                >
                  {{ formatValue(currentResultData.train_r2) }}
                </span>
                <Button
                  size="icon"
                  variant="ghost"
                  class="h-6 w-6"
                  @click="copyValueToClipboard(currentResultData.train_r2)"
                >
                  <ClipboardCopyIcon class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button @click="isDialogOpen = false">关闭</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- 日志查看器 -->
  <LogViewer
    :is-open="showLogViewer"
    :task-id="props.taskId"
    :server-id="props.serverInfo.server_id"
    :total-samples="props.totalSamples"
    :training-progress="props.trainingProgress"
    @update:is-open="showLogViewer = $event"
  />
</template>

<script setup>
import { useTaskStore } from '@renderer/store'
import { ChevronDown, ClipboardCopyIcon, FileText, InfoIcon, ListIcon } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'
import LogViewer from './LogViewer.vue'

const props = defineProps({
  // 任务ID
  taskId: {
    type: String,
    default: '',
  },
  // 是否已完成
  completed: {
    type: Boolean,
    default: false,
  },
  // 服务器信息
  serverInfo: {
    type: Object,
    default: () => ({ server_id: '', service_name: '' }),
  },
  totalSamples: {
    type: Number,
    default: 0,
  },
  // 直接传递的结果数据，避免重复请求
  resultData: {
    type: Object,
    default: null,
  },
  // 任务状态，从父组件传递
  taskStatus: {
    type: String,
    default: 'Initializing',
  },
  // 任务持续时间，从父组件传递
  taskDuration: {
    type: String,
    default: '--',
  },
  // 训练进度，从父组件传递
  trainingProgress: {
    type: Number,
    default: 0,
  },
})

const taskStore = useTaskStore()
const isDialogOpen = ref(false)
const showLogViewer = ref(false)

// 使用传递的结果数据或本地状态
const currentResultData = computed(() => props.resultData || null)
const hasResult = computed(
  () => currentResultData.value !== null && typeof currentResultData.value === 'object',
)

// 是否应该显示结果组件
const shouldShowResult = computed(() => {
  // 如果有结果数据，也显示组件
  if (hasResult.value) return true

  // 如果标记为完成有任务ID，也显示组件
  if (props.completed && props.taskId) return true

  return false
})

// 使用传递的任务状态或从 store 获取
const currentTaskStatus = computed(() => {
  return (
    props.taskStatus ||
    (() => {
      if (!props.taskId) return 'Initializing'
      const task = taskStore.tasks.find((t) => t.taskId === props.taskId)
      return task?.taskStatus || 'Initializing'
    })()
  )
})

// 获取状态文本
const getStatusText = computed(() => {
  return taskStore.getStatusText(currentTaskStatus.value)
})

// 获取状态样式
const getStatusClass = computed(() => {
  return taskStore.getStatusClass(currentTaskStatus.value)
})

// 使用传递的任务持续时间或从 store 获取
const currentTaskDuration = computed(() => {
  return props.taskDuration !== '--'
    ? props.taskDuration
    : (() => {
        const task = taskStore.tasks.find((t) => t.taskId === props.taskId)
        return task?.duration || '--'
      })()
})

// 显示结果详情
const showResultDetail = () => {
  isDialogOpen.value = true
}

// 显示任务日志
const showTaskLogs = () => {
  if (!props.taskId) {
    toast.error('无法查看日志', { description: '任务ID不存在' })
    return
  }

  if (!props.serverInfo || !props.serverInfo.server_id) {
    toast.error('无法查看日志', { description: '服务器信息不存在' })
    return
  }

  showLogViewer.value = true
}

// 格式化数值，处理科学计数法
const formatValue = (value) => {
  if (typeof value === 'number') {
    // 如果是非常小或非常大的数字，使用科学计数法
    if (Math.abs(value) < 0.0001 || Math.abs(value) > 10000) {
      return value.toExponential(6)
    }
    // 否则保留合适的小数位数
    return value.toFixed(value % 1 === 0 ? 0 : 6)
  }
  return value || '--'
}

// 获取R²得分的样式类
const getR2ScoreClass = (r2Score) => {
  if (typeof r2Score !== 'number') return ''

  if (r2Score >= 0.9) return 'text-green-600 dark:text-green-400'
  if (r2Score >= 0.7) return 'text-blue-600 dark:text-blue-400'
  if (r2Score >= 0.5) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

// 复制结果到剪贴板
const copyResultToClipboard = () => {
  if (!currentResultData.value) return

  try {
    const formattedResult = JSON.stringify(currentResultData.value, null, 2)
    navigator.clipboard.writeText(formattedResult)
    toast.success('训练结果已复制到剪贴板')
  } catch (error) {
    toast.error('复制失败', { description: error.message })
  }
}

// 复制单个值到剪贴板
const copyValueToClipboard = (value) => {
  try {
    navigator.clipboard.writeText(String(value))
    toast.success('值已复制到剪贴板')
  } catch (error) {
    toast.error('复制失败', { description: error.message })
  }
}
</script>
