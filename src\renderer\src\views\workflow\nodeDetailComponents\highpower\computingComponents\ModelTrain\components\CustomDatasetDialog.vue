<template>
  <Dialog :open="open" @update:open="updateOpen">
    <DialogContent class="max-w-6xl h-[80vh] flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle class="flex items-center gap-2">
          <LucideIcon name="Settings" class="w-5 h-5 text-neutral-600" />
          自定义数据集配置
        </DialogTitle>
        <DialogDescription>
          您可以通过拖拽或点击下拉菜单来重新分配数据集类型。点击类型标签可以筛选显示对应的数据集。
        </DialogDescription>
      </DialogHeader>

      <div class="flex flex-col flex-1 gap-4 min-h-0">
        <!-- 顶部筛选和统计区域 -->
        <div class="flex items-center justify-between flex-shrink-0">
          <!-- 类型筛选标签 -->
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-neutral-700">筛选类型：</span>
            <div class="flex items-center gap-1">
              <!-- 全部按钮放在最前面 -->
              <Button
                variant="outline"
                size="sm"
                class="h-8 px-3 transition-all duration-200"
                :class="[
                  activeFilter === 'all'
                    ? 'bg-blue-50 text-blue-700 border-blue-300 border-2'
                    : 'hover:bg-neutral-50',
                ]"
                @click="setActiveFilter('all')"
              >
                <span class="text-xs">全部</span>
                <Badge variant="secondary" class="ml-2 text-xs">
                  {{ localDatasetItems.length }}
                </Badge>
              </Button>
              <!-- 其他类型按钮 -->
              <Button
                v-for="type in datasetTypes"
                :key="type.key"
                variant="outline"
                size="sm"
                class="h-8 px-3 transition-all duration-200"
                :class="[
                  activeFilter === type.key
                    ? `${type.activeClass} border-2`
                    : 'hover:bg-neutral-50',
                ]"
                @click="setActiveFilter(type.key)"
              >
                <div class="w-2 h-2 rounded-full mr-2" :class="type.colorClass"></div>
                <span class="text-xs">{{ type.label }}</span>
                <Badge variant="secondary" class="ml-2 text-xs">
                  {{ getTypeCount(type.key) }}
                </Badge>
              </Button>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="flex items-center gap-2">
            <div class="relative">
              <LucideIcon
                name="Search"
                class="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400"
              />
              <Input
                v-model="searchQuery"
                placeholder="搜索数据集文件..."
                class="pl-8 w-64 h-8 text-xs"
              />
            </div>
          </div>
        </div>

        <!-- 单一类型视图（当筛选特定类型时显示） -->
        <div
          v-if="activeFilter !== 'all'"
          class="flex-1 border border-neutral-200 rounded-lg overflow-hidden min-h-0 flex flex-col"
        >
          <div class="p-3 border-b border-neutral-200 bg-neutral-50 flex-shrink-0">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 rounded-full" :class="getActiveTypeConfig()?.colorClass"></div>
                <span class="text-sm font-medium text-neutral-700">
                  {{ getActiveTypeConfig()?.label }}
                </span>
              </div>
              <Badge variant="secondary" class="text-xs">
                {{ getFilteredItems(activeFilter).length }}
              </Badge>
            </div>
          </div>
          <div class="p-4 overflow-y-auto flex-1 min-h-[400px] max-h-[60vh]">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <div
                v-for="item in getFilteredItems(activeFilter)"
                :key="item.id"
                class="rounded-lg p-3 cursor-move hover:shadow-sm transition-all duration-200 group"
                :class="[
                  getItemBgClass(item.type),
                  draggedItem?.id === item.id ? 'opacity-50 scale-95' : '',
                ]"
                draggable="true"
                @dragstart="handleDragStart($event, item)"
                @dragend="handleDragEnd"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2 flex-1 min-w-0">
                    <div class="flex-shrink-0">
                      <div class="w-6 h-6 bg-neutral-100 rounded flex items-center justify-center">
                        <LucideIcon name="FileText" class="w-3 h-3 text-neutral-600" />
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-xs font-medium text-neutral-900 truncate" :title="item.name">
                        {{ item.name }}
                      </p>
                      <p class="text-xs text-neutral-500 truncate" :title="item.rawData?.file_path">
                        {{ getShortPath(item.rawData?.file_path) }}
                      </p>
                    </div>
                  </div>
                  <div
                    class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm" class="h-6 w-6 p-0">
                          <LucideIcon name="MoreVertical" class="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" class="w-32">
                        <DropdownMenuItem
                          v-for="targetType in datasetTypes"
                          :key="targetType.key"
                          class="flex items-center gap-2 cursor-pointer text-xs"
                          @click="changeItemType(item.id, targetType.key)"
                        >
                          <div class="w-2 h-2 rounded-full" :class="targetType.colorClass"></div>
                          <span>移至{{ targetType.label }}</span>
                          <LucideIcon
                            v-if="item.type === targetType.key"
                            name="Check"
                            class="w-3 h-3 ml-auto text-green-600"
                          />
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <Button
                      variant="ghost"
                      size="sm"
                      class="h-6 w-6 p-0"
                      @click="handleChartClick(item)"
                    >
                      <LucideIcon name="Edit2" class="w-3 h-3 text-neutral-500" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态提示 -->
            <div
              v-if="getFilteredItems(activeFilter).length === 0"
              class="flex flex-col items-center justify-center h-full text-neutral-400"
            >
              <LucideIcon name="Database" class="w-12 h-12 mb-4" />
              <p class="text-sm font-medium text-center">
                暂无{{ getActiveTypeConfig()?.label }}数据
              </p>
              <p class="text-xs text-neutral-400 mt-1 text-center">
                您可以通过拖拽或下拉菜单将其他数据集移动到此类型
              </p>
            </div>
          </div>
        </div>

        <!-- 全部数据集视图（4列拖拽布局） -->
        <div
          v-if="activeFilter === 'all'"
          class="flex-1 grid grid-cols-4 gap-4 min-h-0 overflow-hidden"
        >
          <!-- 每个类型的拖拽区域 -->
          <div
            v-for="type in datasetTypes"
            :key="type.key"
            class="flex flex-col border-2 border-dashed rounded-lg transition-all duration-200"
            :class="[
              dragOverType === type.key
                ? `${type.dragOverClass} border-solid`
                : 'border-neutral-200 hover:border-neutral-300',
            ]"
            @dragover.prevent="handleDragOver(type.key)"
            @dragleave="handleDragLeave"
            @drop="handleDrop($event, type.key)"
          >
            <!-- 类型标题 -->
            <div class="p-3 border-b border-neutral-200 bg-neutral-50 flex-shrink-0">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full" :class="type.colorClass"></div>
                  <span class="text-sm font-medium text-neutral-700">{{ type.label }}</span>
                </div>
                <Badge variant="secondary" class="text-xs">
                  {{ getTypeCount(type.key) }}
                </Badge>
              </div>
            </div>

            <!-- 数据集列表 -->
            <div class="flex-1 p-2 overflow-y-auto min-h-[400px] max-h-[60vh] flex flex-col">
              <div class="space-y-2 flex-1">
                <div
                  v-for="item in getFilteredItems(type.key)"
                  :key="item.id"
                  class="rounded-lg p-3 cursor-move hover:shadow-sm transition-all duration-200 group"
                  :class="[
                    getItemBgClass(item.type),
                    draggedItem?.id === item.id ? 'opacity-50 scale-95' : '',
                  ]"
                  draggable="true"
                  @dragstart="handleDragStart($event, item)"
                  @dragend="handleDragEnd"
                >
                  <div class="flex items-center justify-between">
                    <!-- 左侧：文件信息 -->
                    <div class="flex items-center gap-2 flex-1 min-w-0">
                      <div class="flex-shrink-0">
                        <div
                          class="w-6 h-6 bg-neutral-100 rounded flex items-center justify-center"
                        >
                          <LucideIcon name="FileText" class="w-3 h-3 text-neutral-600" />
                        </div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-xs font-medium text-neutral-900 truncate" :title="item.name">
                          {{ item.name }}
                        </p>
                        <p
                          class="text-xs text-neutral-500 truncate"
                          :title="item.rawData?.file_path"
                        >
                          {{ getShortPath(item.rawData?.file_path) }}
                        </p>
                      </div>
                    </div>

                    <!-- 右侧：操作按钮 -->
                    <div
                      class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <!-- 类型选择下拉菜单 -->
                      <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                          <Button variant="ghost" size="sm" class="h-6 w-6 p-0">
                            <LucideIcon name="MoreVertical" class="w-3 h-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" class="w-32">
                          <DropdownMenuItem
                            v-for="targetType in datasetTypes"
                            :key="targetType.key"
                            class="flex items-center gap-2 cursor-pointer text-xs"
                            @click="changeItemType(item.id, targetType.key)"
                          >
                            <div class="w-2 h-2 rounded-full" :class="targetType.colorClass"></div>
                            <span>移至{{ targetType.label }}</span>
                            <LucideIcon
                              v-if="item.type === targetType.key"
                              name="Check"
                              class="w-3 h-3 ml-auto text-green-600"
                            />
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <!-- 查看图表按钮 -->
                      <Button
                        variant="ghost"
                        size="sm"
                        class="h-6 w-6 p-0"
                        @click="handleChartClick(item)"
                      >
                        <LucideIcon name="Edit2" class="w-3 h-3 text-neutral-500" />
                      </Button>
                    </div>
                  </div>
                </div>

                <!-- 空状态提示 -->
                <div
                  v-if="getFilteredItems(type.key).length === 0"
                  class="flex flex-col items-center justify-center flex-1 text-neutral-400 h-full"
                >
                  <LucideIcon name="Database" class="w-8 h-8 mb-2" />
                  <p class="text-xs text-center">暂无{{ type.label }}数据</p>
                  <p class="text-xs text-center">拖拽文件到此处</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="flex items-center justify-between flex-shrink-0 mt-4">
        <div class="flex items-center gap-4 text-xs text-neutral-500">
          <div class="flex items-center gap-1">
            <LucideIcon name="Info" class="w-3 h-3" />
            <span>共 {{ localDatasetItems.length }} 个数据集文件</span>
          </div>
          <div class="flex items-center gap-1">
            <LucideIcon name="MousePointer" class="w-3 h-3" />
            <span>支持拖拽重新分类</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" @click="handleCancel">取消</Button>
          <Button :disabled="localDatasetItems.length === 0" @click="handleConfirm">
            <LucideIcon name="Check" class="w-4 h-4 mr-1" />
            确认配置
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- 数据集图表弹框 -->
  <DatasetChartDialog
    v-model:open="chartDialogOpen"
    :dataset="selectedDatasetItem || undefined"
    @update-chart-settings="handleChartSettingsUpdate"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { LucideIcon } from '@renderer/components'
import DatasetChartDialog from './DatasetChartDialog.vue'
import { useCustomDataset } from '../composables/useCustomDataset'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

// 使用 defineModel
const open = defineModel<boolean>('open', { default: false })

// 定义 props
const props = defineProps<{
  datasetData: DatasetData
}>()

// 定义 emits
const emit = defineEmits<{
  confirm: [datasetData: DatasetData]
  cancel: []
  'chart-click': [item: DatasetItem]
  'update-chart-settings': [itemId: string, startCycle: number, endCycle: number]
}>()

// 使用组合式函数
const {
  datasetTypes,
  localDatasetItems,
  activeFilter,
  searchQuery,
  draggedItem,
  dragOverType,
  chartDialogOpen,
  selectedDatasetItem,
  filteredItems,
  getTypeCount,
  getFilteredItems,
  getTypeLabel,
  getBadgeClass,
  getItemBgClass,
  getShortPath,
  setActiveFilter,
  getActiveTypeConfig,
  handleDragStart,
  handleDragEnd,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  changeItemType,
  handleChartClick,
  handleConfirm,
  handleCancel,
} = useCustomDataset(
  computed(() => props.datasetData),
  open,
  emit,
)

// 处理图表设置更新
const handleChartSettingsUpdate = (itemId: string, startCycle: number, endCycle: number) => {
  console.log('CustomDatasetDialog - 转发图表设置更新:', { itemId, startCycle, endCycle })

  // 更新本地数据项的 startCycle 和 endCycle
  const item = localDatasetItems.value.find((item) => item.id === itemId)
  if (item) {
    item.startCycle = startCycle
    item.endCycle = endCycle
    console.log('CustomDatasetDialog - 已更新本地数据项:', item)
  }

  // 转发事件给父组件
  emit('update-chart-settings', itemId, startCycle, endCycle)
}

// 更新弹框打开状态
const updateOpen = (value: boolean) => {
  open.value = value
}
</script>

<style scoped>
/* 拖拽时的动画效果 */
.drag-item {
  transition: all 0.2s ease;
}

.drag-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
